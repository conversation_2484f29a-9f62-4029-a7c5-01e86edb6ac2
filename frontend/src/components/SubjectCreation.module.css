.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.backButton {
  background: none;
  border: 1px solid #4a9eff;
  color: #4a9eff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.backButton:hover {
  background-color: #4a9eff;
  color: white;
}

.header h1 {
  color: #4a9eff;
  margin: 0;
  font-size: 24px;
}

.form {
  background-color: #2a2a2a;
  padding: 30px;
  border-radius: 8px;
  border: 1px solid #444;
}

.formGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #ddd;
}

.input, .textarea {
  width: 100%;
  padding: 12px;
  background-color: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #ddd;
  font-size: 14px;
  font-family: inherit;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #4a9eff;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.error {
  background-color: rgba(255, 107, 107, 0.1);
  border: 1px solid #ff6b6b;
  color: #ff6b6b;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.generateButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  transition: background-color 0.2s;
  width: 100%;
}

.generateButton:hover:not(:disabled) {
  background-color: #5a7fb5;
}

.generateButton:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.curriculumPreview {
  background-color: #2a2a2a;
  padding: 30px;
  border-radius: 8px;
  border: 1px solid #444;
}

.subjectInfo {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #444;
}

.subjectInfo h2 {
  color: #4a9eff;
  margin: 0 0 10px 0;
  font-size: 22px;
}

.subjectInfo p {
  color: #ccc;
  margin: 0;
  line-height: 1.5;
}

.curriculum {
  margin-bottom: 30px;
}

.category {
  margin-bottom: 25px;
  padding: 20px;
  background-color: #333;
  border-radius: 6px;
  border: 1px solid #555;
}

.categoryTitle {
  color: #4a9eff;
  margin: 0 0 8px 0;
  font-size: 18px;
}

.categoryDescription {
  color: #aaa;
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.4;
}

.topicList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topic {
  display: flex;
  flex-direction: column;
  padding: 10px 0;
  border-bottom: 1px solid #444;
}

.topic:last-child {
  border-bottom: none;
}

.topicTitle {
  color: #ddd;
  font-weight: 500;
  margin-bottom: 4px;
}

.topicDescription {
  color: #aaa;
  font-size: 13px;
  line-height: 1.3;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.editButton {
  background: none;
  border: 1px solid #4a9eff;
  color: #4a9eff;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s, color 0.2s;
}

.editButton:hover {
  background-color: #4a9eff;
  color: white;
}

.createButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.createButton:hover {
  background-color: #5a7fb5;
}

.editorContainer {
  background-color: #2a2a2a;
  padding: 30px;
  border-radius: 8px;
  border: 1px solid #444;
}

.editorContainer .subjectInfo {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #444;
}

.editorContainer .actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #444;
}
