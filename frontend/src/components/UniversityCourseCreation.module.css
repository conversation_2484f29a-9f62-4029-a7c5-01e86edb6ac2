.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #333;
}

.header h1 {
  margin: 0;
  color: #fff;
  font-size: 2rem;
  font-weight: 600;
}

.backButton {
  padding: 10px 20px;
  background: #333;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.backButton:hover {
  background: #444;
}

.languageIndicator {
  padding: 8px 16px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #4CAF50;
}

.sketchSection {
  max-width: 800px;
  margin: 0 auto;
}

.instructions {
  background: #222;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #333;
}

.instructions h2 {
  margin: 0 0 16px 0;
  color: #4CAF50;
  font-size: 1.5rem;
}

.instructions p {
  margin: 0 0 16px 0;
  line-height: 1.6;
  color: #ccc;
}

.examples {
  background: #2a2a2a;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.examples strong {
  color: #4CAF50;
  display: block;
  margin-bottom: 8px;
}

.examples ul {
  margin: 0;
  padding-left: 20px;
  color: #ddd;
}

.examples li {
  margin-bottom: 4px;
}

.sketchInput {
  width: 100%;
  min-height: 400px;
  padding: 20px;
  background: #222;
  border: 2px solid #333;
  border-radius: 12px;
  color: #fff;
  font-size: 1rem;
  line-height: 1.6;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  resize: vertical;
  margin-bottom: 20px;
}

.sketchInput:focus {
  outline: none;
  border-color: #4CAF50;
}

.sketchInput::placeholder {
  color: #666;
}

.error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #f44336;
  color: #f44336;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.generateButton {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.generateButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.generateButton:disabled {
  background: #555;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.buttonGroup {
  display: flex;
  gap: 16px;
  align-items: center;
}

.demoButton {
  padding: 16px 24px;
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demoButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #F57C00, #FF9800);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.demoButton:disabled {
  background: #555;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Generation Progress Styles */
.generationProgress {
  background: #222;
  border-radius: 12px;
  padding: 32px;
  border: 2px solid #4CAF50;
  text-align: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { border-color: #4CAF50; }
  50% { border-color: #45a049; }
  100% { border-color: #4CAF50; }
}

.progressHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

.progressIcon {
  font-size: 2rem;
  animation: bounce 1.5s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.progressHeader h3 {
  margin: 0;
  color: #4CAF50;
  font-size: 1.5rem;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049, #4CAF50);
  background-size: 200% 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.progressText {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.progressPercentage {
  font-size: 1.2rem;
  font-weight: bold;
  color: #4CAF50;
}

.progressStage {
  color: #ddd;
  font-style: italic;
  max-width: 60%;
  text-align: right;
}

.progressDetails {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.progressStep {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #333;
}

.stepIcon {
  font-size: 1.2rem;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progressStep span {
  color: #ccc;
  font-size: 0.9rem;
}

.estimatedTime {
  color: #888;
  font-size: 0.9rem;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #333;
}

.coursePreview {
  background: #222;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #333;
}

.courseHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #333;
}

.courseHeader h2 {
  margin: 0;
  color: #4CAF50;
  font-size: 1.8rem;
}

.courseMetadata {
  display: flex;
  gap: 16px;
}

.duration, .difficulty {
  padding: 6px 12px;
  background: #333;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #ddd;
}

.courseDescription {
  margin-bottom: 24px;
  padding: 16px;
  background: #2a2a2a;
  border-radius: 8px;
  line-height: 1.6;
  color: #ddd;
}

.learningOutcomes {
  margin-bottom: 24px;
}

.learningOutcomes h3 {
  color: #4CAF50;
  margin-bottom: 12px;
}

.learningOutcomes ul {
  list-style: none;
  padding: 0;
}

.learningOutcomes li {
  padding: 8px 0;
  padding-left: 24px;
  position: relative;
  color: #ddd;
}

.learningOutcomes li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
}

.modules h3 {
  color: #4CAF50;
  margin-bottom: 16px;
}

.module {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #333;
}

.moduleHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.moduleHeader h4 {
  margin: 0;
  color: #fff;
  font-size: 1.2rem;
}

.moduleDuration {
  background: #333;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #ccc;
}

.moduleDescription {
  color: #ccc;
  line-height: 1.5;
  margin-bottom: 16px;
}

.moduleObjectives {
  margin-bottom: 16px;
}

.moduleObjectives strong {
  color: #4CAF50;
  display: block;
  margin-bottom: 8px;
}

.moduleObjectives ul {
  margin: 0;
  padding-left: 20px;
  color: #ddd;
}

.moduleObjectives li {
  margin-bottom: 4px;
}

.moduleCurriculum strong {
  color: #4CAF50;
  display: block;
  margin-bottom: 8px;
}

.category {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #333;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 2px 4px 2px 0;
  font-size: 0.85rem;
}

.categoryTitle {
  color: #fff;
}

.topicCount {
  color: #888;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #333;
}

.editButton, .createButton, .regenerateButton {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton {
  background: #666;
  color: #fff;
}

.editButton:hover {
  background: #777;
}

.regenerateButton {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.regenerateButton:hover {
  background: linear-gradient(135deg, #F57C00, #FF9800);
  transform: translateY(-1px);
}

.createButton {
  background: #4CAF50;
  color: white;
}

.createButton:hover {
  background: #45a049;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .courseHeader {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .courseMetadata {
    justify-content: center;
  }

  .actions {
    flex-direction: column;
    gap: 12px;
  }

  .editButton, .createButton, .regenerateButton {
    width: 100%;
  }

  .buttonGroup {
    flex-direction: column;
    gap: 12px;
  }

  .generateButton, .demoButton {
    width: 100%;
  }

  .progressDetails {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .progressText {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .progressStage {
    max-width: 100%;
    text-align: center;
  }

  .generationProgress {
    padding: 24px 16px;
  }

  .progressHeader h3 {
    font-size: 1.3rem;
  }
}
