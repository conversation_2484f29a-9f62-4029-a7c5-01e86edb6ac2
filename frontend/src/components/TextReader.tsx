import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// quick helper for merging tailwind classes
function cn(...inputs: any[]): string {
  return twMerge(clsx(inputs));
}

const THEMES = {
  light: {
    bg: 'bg-white',
    text: 'text-gray-900',
    highlight: 'bg-yellow-300 text-black',
    controls: 'bg-slate-100 border-slate-200',
  },
  dark: {
    bg: 'bg-gray-900',
    text: 'text-gray-100',
    highlight: 'bg-sky-400 text-white',
    controls: 'bg-gray-800 border-gray-700',
  },
};

const VIEW_MODES = {
  FULL: 'full',
  MULTILINE: 'multiline',
  SINGLE: 'single',
};

interface TextReaderProps {
  text: string;
  lang?: string;
  rate?: number;
  theme?: 'light' | 'dark';
  fontSize?: string;
  className?: string;
}

const TextReader: React.FC<TextReaderProps> = ({
  text,
  lang = 'en-US',
  rate = 0.9,
  theme = 'dark',
  fontSize = 'text-lg',
  className,
}) => {
  const [currentWordIndex, setCurrentWordIndex] = useState<number>(-1);
  const [currentLineIndex, setCurrentLineIndex] = useState<number>(0);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isSupported, setIsSupported] = useState<boolean>(true);
  const [viewMode, setViewMode] = useState<string>(VIEW_MODES.FULL);
  const [lineData, setLineData] = useState<any[]>([]);

  const words = useRef<string[]>(text.split(/\s+/).filter(Boolean));
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  /* ---------- speech synthesis plumbing ---------- */
  useEffect(() => {
    if (typeof window === 'undefined' || !('speechSynthesis' in window)) {
      setIsSupported(false);
      return;
    }
    synthRef.current = window.speechSynthesis;
    words.current = text.split(/\s+/).filter(Boolean);
    return () => synthRef.current?.cancel();
  }, [text]);

  useEffect(() => {
    if (!isPlaying || !synthRef.current) return;
    const handleBoundary = (e: SpeechSynthesisEvent): void => {
      if (e.name === 'word') {
        const idx = words.current.findIndex((_, i) => {
          const start =
            words.current.slice(0, i).join(' ').length + (i ? 1 : 0);
          const end = start + words.current[i].length;
          return e.charIndex >= start && e.charIndex < end;
        });
        if (idx !== -1 && idx !== currentWordIndex) setCurrentWordIndex(idx);
      }
    };
    const handleEnd = (): void => {
      setIsPlaying(false);
      setCurrentWordIndex(-1);
      setCurrentLineIndex(0);
    };

    utteranceRef.current = new SpeechSynthesisUtterance(text);
    utteranceRef.current.lang = lang;
    utteranceRef.current.rate = rate;
    utteranceRef.current.onboundary = handleBoundary;
    utteranceRef.current.onend = handleEnd;
    synthRef.current.speak(utteranceRef.current);

    return () => {
      if (utteranceRef.current) {
        utteranceRef.current.onboundary = null;
        utteranceRef.current.onend = null;
      }
    };
  }, [isPlaying, text, lang, rate]);

  /* ---------- helpers ---------- */
  const play = (): void => {
    if (isPlaying) synthRef.current?.pause();
    else {
      if (synthRef.current?.paused)
            synthRef.current.resume();
      else setIsPlaying(true);
    }
  };

  const stop = (): void => {
    synthRef.current?.cancel();
    setIsPlaying(false);
    setCurrentWordIndex(-1);
    setCurrentLineIndex(0);
  };

  /* ---------- split into lines for multiline/single ---------- */
  useEffect(() => {
    const arr = [];
    if (viewMode === VIEW_MODES.FULL) {
      // For full view, create one line with all words
      setLineData([{
        text: text,
        words: words.current,
        startWordIndex: 0,
        endWordIndex: words.current.length - 1
      }]);
      return;
    }
    
    let line = '';
    let wordIndex = 0;
    for (const w of words.current) {
      if ((line + w).length > 60) {
        const lineWords = line.trim().split(/\s+/);
        arr.push({
          text: line.trim(),
          words: lineWords,
          startWordIndex: wordIndex,
          endWordIndex: wordIndex + lineWords.length - 1
        });
        wordIndex += lineWords.length;
        line = '';
      }
      line += w + ' ';
    }
    if (line.trim()) {
      const lineWords = line.trim().split(/\s+/);
      arr.push({
        text: line.trim(),
        words: lineWords,
        startWordIndex: wordIndex,
        endWordIndex: wordIndex + lineWords.length - 1
      });
    }
    setLineData(arr);
  }, [text, viewMode]);

  /* ---------- update current line when word changes ---------- */
  useEffect(() => {
    if (viewMode === VIEW_MODES.MULTILINE || viewMode === VIEW_MODES.SINGLE) {
      for (let i = 0; i < lineData.length; i++) {
        const { startWordIndex, endWordIndex } = lineData[i];
        if (currentWordIndex >= startWordIndex && currentWordIndex <= endWordIndex) {
          setCurrentLineIndex(i);
          break;
        }
      }
    }
  }, [currentWordIndex, lineData, viewMode]);

  /* ---------- scroll to current line in multiline mode ---------- */
  useEffect(() => {
    if (viewMode === VIEW_MODES.MULTILINE && containerRef.current && lineData.length > 0) {
      const lineHeight = 32; // h-8 = 2rem = 32px
      const containerHeight = 128; // h-32 = 8rem = 128px
      const scrollTop = currentLineIndex * lineHeight - (containerHeight / 2) + (lineHeight / 2);
      
      containerRef.current.scrollTo({
        top: Math.max(0, scrollTop),
        behavior: 'smooth'
      });
    }
  }, [currentLineIndex, viewMode, lineData]);

  if (!isSupported)
    return (
      <p className="p-4 text-red-400">
        Speech synthesis not supported in this browser.
      </p>
    );

  const T = THEMES[theme];

  /* ---------- render helpers ---------- */
  const renderFull = () => (
    <p
      className={cn('leading-relaxed tracking-wide', fontSize, T.text)}
      style={{ lineHeight: 1.9 }}
    >
      {words.current.map((w, i) => (
        <span
          key={i}
          className={cn(
            'transition-colors duration-300 rounded px-1',
            i === currentWordIndex ? T.highlight : ''
          )}
        >
          {w}{' '}
        </span>
      ))}
    </p>
  );

  const renderMultiline = () => (
    <div 
      ref={containerRef}
      className="h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-gray-500 scrollbar-track-gray-800"
    >
      <div className="space-y-1">
        {lineData.map((line, i) => (
          <div
            key={i}
            className={cn(
              'h-8 leading-8 rounded px-2 transition-all duration-300',
              fontSize,
              T.text,
              i === currentLineIndex ? 'bg-gray-700/50' : ''
            )}
          >
            {line.words.map((word, wordIdx) => {
              const globalWordIndex = line.startWordIndex + wordIdx;
              const isCurrent = globalWordIndex === currentWordIndex;
              return (
                <span
                  key={wordIdx}
                  className={cn(
                    'transition-colors duration-300 rounded px-1',
                    isCurrent ? T.highlight : ''
                  )}
                >
                  {word}{' '}
                </span>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );

  const renderSingle = () => (
    <div className="h-10 flex items-center justify-center overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentLineIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className={cn('font-semibold whitespace-nowrap', fontSize, T.text)}
        >
          {lineData[currentLineIndex]?.words.map((word, wordIdx) => {
            const globalWordIndex = lineData[currentLineIndex].startWordIndex + wordIdx;
            const isCurrent = globalWordIndex === currentWordIndex;
            return (
              <span
                key={wordIdx}
                className={cn(
                  'transition-colors duration-300 rounded px-1',
                  isCurrent ? T.highlight : ''
                )}
              >
                {word}{' '}
              </span>
            );
          })}
        </motion.div>
      </AnimatePresence>
    </div>
  );

  return (
    <div
      className={cn(
        'rounded-xl shadow-2xl p-4 sm:p-6 space-y-4',
        T.bg,
        className
      )}
    >
      {/* view switcher */}
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          {Object.values(VIEW_MODES).map((m) => (
            <button
              key={m}
              onClick={() => setViewMode(m)}
              className={cn(
                'px-3 py-1 rounded text-sm transition capitalize',
                viewMode === m
                  ? T.highlight
                  : 'hover:bg-gray-500/20 text-gray-400'
              )}
            >
              {m}
            </button>
          ))}
        </div>
      </div>

      {/* text area */}
      <div>
        {viewMode === VIEW_MODES.FULL && renderFull()}
        {viewMode === VIEW_MODES.MULTILINE && renderMultiline()}
        {viewMode === VIEW_MODES.SINGLE && renderSingle()}
      </div>

      {/* controls */}
      <div className="flex gap-4 justify-center">
        <button
          onClick={play}
          className={cn(
            'px-6 py-2 text-base rounded-lg font-semibold transition transform hover:scale-105 active:scale-95',
            isPlaying
              ? 'bg-orange-500 hover:bg-orange-600 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white'
          )}
        >
          {isPlaying ? 'Pause' : 'Play'}
        </button>
        <button
          onClick={stop}
          className="px-6 text-base py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-semibold transition transform hover:scale-105 active:scale-95"
        >
          Stop
        </button>
      </div>
    </div>
  );
};

export default TextReader;