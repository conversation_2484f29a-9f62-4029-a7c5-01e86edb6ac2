.dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.headerButtons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header h1 {
  color: #4a9eff;
  margin: 0;
  font-size: 28px;
}

.createButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.createButton:hover {
  background-color: #5a7fb5;
}

.universityButton {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.universityButton:hover {
  background: linear-gradient(135deg, #45a049, #4CAF50);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #4a9eff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loadingText {
  margin-top: 20px;
  color: #ccc;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  color: #ccc;
}

.errorContainer h2 {
  color: #ff6b6b;
  margin-bottom: 10px;
}

.retryButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 15px;
}

.retryButton:hover {
  background-color: #5a7fb5;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  color: #ccc;
}

.emptyState h2 {
  color: #4a9eff;
  margin-bottom: 10px;
}

.createButtonLarge {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  margin-top: 20px;
  transition: background-color 0.2s;
}

.createButtonLarge:hover {
  background-color: #5a7fb5;
}

.subjectsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.subjectCard {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #444;
  transition: border-color 0.2s, transform 0.2s;
}

.subjectCard:hover {
  border-color: #4a9eff;
  transform: translateY(-2px);
}

.subjectHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.subjectTitle {
  color: #4a9eff;
  margin: 0;
  font-size: 18px;
  flex: 1;
}

.subjectActions {
  display: flex;
  gap: 5px;
}

.deleteButton {
  background: none;
  border: none;
  color: #ff6b6b;
  cursor: pointer;
  font-size: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.deleteButton:hover {
  background-color: rgba(255, 107, 107, 0.1);
}

.subjectDescription {
  color: #ccc;
  margin: 10px 0;
  font-size: 14px;
  line-height: 1.4;
}

.subjectMeta {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin: 15px 0;
  font-size: 12px;
}

.curriculumStatus {
  color: #4a9eff;
}

.createdDate {
  color: #888;
}

.selectButton {
  width: 100%;
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.selectButton:hover:not(:disabled) {
  background-color: #5a7fb5;
}

.selectButton:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}
