import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import styles from './LanguageSettings.module.css';

function LanguageSettings({ isOpen, onClose }) {
  const { selectedLanguage, changeLanguage, supportedLanguages } = useLanguage();
  const [tempSelectedLanguage, setTempSelectedLanguage] = useState(selectedLanguage.code);

  if (!isOpen) return null;

  const handleSave = () => {
    changeLanguage(tempSelectedLanguage);
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedLanguage(selectedLanguage.code);
    onClose();
  };

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2>Language Settings</h2>
          <button className={styles.closeButton} onClick={handleCancel}>
            ×
          </button>
        </div>
        
        <div className={styles.content}>
          <p className={styles.description}>
            Select your preferred language for AI-generated content. This will affect curriculum generation and course content.
          </p>
          
          <div className={styles.languageGrid}>
            {supportedLanguages.map((language) => (
              <label key={language.code} className={styles.languageOption}>
                <input
                  type="radio"
                  name="language"
                  value={language.code}
                  checked={tempSelectedLanguage === language.code}
                  onChange={(e) => setTempSelectedLanguage(e.target.value)}
                  className={styles.radioInput}
                />
                <div className={styles.languageInfo}>
                  <span className={styles.languageName}>{language.name}</span>
                  <span className={styles.nativeName}>{language.nativeName}</span>
                </div>
              </label>
            ))}
          </div>
        </div>
        
        <div className={styles.footer}>
          <button className={styles.cancelButton} onClick={handleCancel}>
            Cancel
          </button>
          <button className={styles.saveButton} onClick={handleSave}>
            Save Language
          </button>
        </div>
      </div>
    </div>
  );
}

export default LanguageSettings;
