.code-canvas-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 70vh;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.code-canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.code-canvas-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.canvas-actions {
  display: flex;
  gap: 8px;
}

.run-btn,
.reset-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.run-btn {
  background-color: #4a6cf7;
  color: white;
}

.run-btn:hover {
  background-color: #3a5ce5;
}

.reset-btn {
  background-color: #f1f3f5;
  color: #495057;
}

.reset-btn:hover {
  background-color: #e9ecef;
}

.canvas-tabs {
  display: flex;
  background-color: #f1f3f5;
  border-bottom: 1px solid #e0e0e0;
}

.tab-btn {
  padding: 10px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: #495057;
  transition: all 0.2s ease;
  position: relative;
}

.tab-btn:hover {
  background-color: #e9ecef;
}

.tab-btn.active {
  color: #4a6cf7;
  background-color: #fff;
}

.tab-btn.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #4a6cf7;
}

.canvas-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.code-editor-container,
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header,
.preview-header {
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-size: 0.9rem;
  color: #6c757d;
}

.code-editor-wrapper {
  position: relative;
  flex: 1;
  overflow: hidden;
}

.code-editor-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 16px;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 14px;
  line-height: 1.5;
  border: none;
  resize: none;
  outline: none;
  background-color: transparent;
  color: transparent;
  caret-color: #333;
  z-index: 2;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
  overflow-y: auto;
}

.code-editor-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 16px;
  margin: 0;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 14px;
  line-height: 1.5;
  border: none;
  resize: none;
  outline: none;
  background-color: #fff;
  overflow: auto;
  z-index: 1;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
  overflow-y: auto;
}

/* Prism.js theme customization */
.code-editor-highlight pre[class*="language-"] {
  margin: 0;
  padding: 0;
  background: transparent;
  overflow: visible;
}

.code-editor-highlight code[class*="language-"] {
  color: #333;
  text-shadow: none;
  background: transparent;
}

.preview-container iframe {
  background-color: #fff;
  width: 100%;
  height: 100%;
}
