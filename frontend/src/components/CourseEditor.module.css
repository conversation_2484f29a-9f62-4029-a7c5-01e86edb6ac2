.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #333;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 20px;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 16px;
}

.backButton {
  padding: 10px 20px;
  background: #333;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.backButton:hover {
  background: #444;
}

.header h1 {
  margin: 0;
  color: #fff;
  font-size: 2rem;
  font-weight: 600;
}

.languageIndicator {
  padding: 8px 16px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #4CAF50;
}

.regenerateButton {
  padding: 10px 20px;
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s;
}

.regenerateButton:hover {
  background: linear-gradient(135deg, #F57C00, #FF9800);
  transform: translateY(-1px);
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  gap: 4px;
  margin-bottom: 30px;
  background: #222;
  border-radius: 8px;
  padding: 4px;
}

.tab {
  flex: 1;
  padding: 12px 20px;
  background: transparent;
  color: #ccc;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s;
}

.tab:hover {
  background: #333;
  color: #fff;
}

.activeTab {
  background: #4CAF50 !important;
  color: white !important;
}

/* Content */
.content {
  margin-bottom: 30px;
}

/* Overview Tab */
.overviewTab {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.section {
  background: #222;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #333;
}

.section h3 {
  margin: 0 0 20px 0;
  color: #4CAF50;
  font-size: 1.3rem;
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.sectionHeader h3 {
  margin: 0;
}

.formGroup {
  margin-bottom: 20px;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  color: #4CAF50;
  font-weight: 500;
}

.input, .textarea, .select {
  width: 100%;
  padding: 12px;
  background: #333;
  border: 1px solid #444;
  border-radius: 6px;
  color: #fff;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.input:focus, .textarea:focus, .select:focus {
  outline: none;
  border-color: #4CAF50;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.addButton {
  padding: 8px 16px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.addButton:hover {
  background: #45a049;
}

/* Learning Outcomes */
.outcomesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.outcomeItem {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.outcomeInput {
  flex: 1;
  padding: 12px;
  background: #333;
  border: 1px solid #444;
  border-radius: 6px;
  color: #fff;
  font-size: 1rem;
  resize: vertical;
  min-height: 60px;
}

.outcomeInput:focus {
  outline: none;
  border-color: #4CAF50;
}

.removeButton {
  width: 32px;
  height: 32px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  flex-shrink: 0;
  margin-top: 12px;
}

.removeButton:hover {
  background: #d32f2f;
}

/* Modules Tab */
.modulesTab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modulesHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20px;
}

.modulesHeader h3 {
  margin: 0 0 8px 0;
  color: #4CAF50;
  font-size: 1.3rem;
}

.modulesHeader p {
  margin: 0;
  color: #ccc;
  font-size: 0.9rem;
}

.addModuleButton {
  padding: 12px 20px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
}

.addModuleButton:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.modulesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.moduleCard {
  background: #222;
  border-radius: 12px;
  border: 1px solid #333;
  overflow: hidden;
  transition: border-color 0.2s;
}

.moduleCard:hover {
  border-color: #4CAF50;
}

.moduleHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
}

.moduleInfo {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.moduleOrder {
  background: #4CAF50;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.moduleInfo h4 {
  margin: 0;
  color: #fff;
  font-size: 1.1rem;
  flex: 1;
}

.moduleDuration {
  background: #333;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #ccc;
}

.moduleActions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.moveButton, .expandButton, .deleteButton {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.moveButton {
  background: #666;
  color: white;
}

.moveButton:hover {
  background: #777;
}

.expandButton {
  background: #4CAF50;
  color: white;
}

.expandButton:hover {
  background: #45a049;
}

.deleteButton {
  background: #f44336;
  color: white;
}

.deleteButton:hover {
  background: #d32f2f;
}

.moduleContent {
  padding: 0 20px 20px 20px;
  border-top: 1px solid #333;
  background: #2a2a2a;
}

.objectivesSection {
  margin-top: 20px;
}

.objectivesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.objectiveItem {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.objectiveInput {
  flex: 1;
  padding: 12px;
  background: #333;
  border: 1px solid #444;
  border-radius: 6px;
  color: #fff;
  font-size: 1rem;
  resize: vertical;
  min-height: 60px;
}

.objectiveInput:focus {
  outline: none;
  border-color: #4CAF50;
}

/* Curriculum Categories */
.curriculumSection {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #444;
}

.categoriesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.categoryCard {
  background: #333;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #444;
}

.categoryHeader {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.categoryTitleInput {
  flex: 1;
  padding: 8px 12px;
  background: #2a2a2a;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
}

.categoryTitleInput:focus {
  outline: none;
  border-color: #4CAF50;
}

.topicsSection {
  margin-top: 12px;
}

.topicsHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.topicsHeader span {
  color: #ccc;
  font-size: 0.9rem;
  font-weight: 500;
}

.addTopicButton {
  padding: 4px 8px;
  background: #666;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.addTopicButton:hover {
  background: #777;
}

.topicsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.topicItem {
  display: flex;
  gap: 8px;
  align-items: center;
}

.topicInput {
  flex: 1;
  padding: 6px 10px;
  background: #2a2a2a;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 0.9rem;
}

.topicInput:focus {
  outline: none;
  border-color: #4CAF50;
}

.removeTopicButton {
  width: 24px;
  height: 24px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.removeTopicButton:hover {
  background: #d32f2f;
}

/* Footer */
.footer {
  position: sticky;
  bottom: 0;
  background: #1a1a1a;
  padding: 20px 0;
  border-top: 2px solid #333;
  margin-top: 30px;
}

.footerActions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.cancelButton, .saveButton {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s;
}

.cancelButton {
  background: #666;
  color: white;
}

.cancelButton:hover {
  background: #777;
}

.saveButton {
  background: #4CAF50;
  color: white;
}

.saveButton:hover {
  background: #45a049;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .headerLeft, .headerRight {
    flex-direction: column;
    gap: 12px;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .moduleHeader {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .moduleInfo {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    width: 100%;
  }
  
  .moduleActions {
    align-self: flex-end;
  }
  
  .footerActions {
    flex-direction: column;
  }

  .modulesHeader {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .addModuleButton {
    width: 100%;
  }

  .categoryHeader {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .topicsHeader {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .addTopicButton {
    align-self: flex-start;
  }
}
