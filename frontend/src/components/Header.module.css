.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #333;
  border-bottom: 1px solid #444;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #4a9eff;
  margin: 0;
}

.headerButtons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.languageButton,
.settingsButton {
  padding: 8px 16px;
  background-color: #444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.languageButton:hover,
.settingsButton:hover {
  background-color: #555;
}

.languageButton {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}
