import React from 'react';
import ReactMarkdown from 'react-markdown';
import styles from './ContentDisplay.module.css';

// Types
interface Topic {
  id?: string;
  title: string;
  [key: string]: any;
}

interface ContentDisplayProps {
  content: string;
  isLoading: boolean;
  selectedTopic: Topic | null;
}

const ContentDisplay: React.FC<ContentDisplayProps> = ({ content, isLoading, selectedTopic }) => {
  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p className={styles.loadingText}>Generating your course...</p>
      </div>
    );
  }

  if (!selectedTopic) {
    return (
      <div className={styles.emptyState}>
        <h2>Welcome to QtMaster.io</h2>
        <p>Select a topic from the curriculum to get started.</p>
      </div>
    );
  }

  if (!content) {
    return (
      <div className={styles.emptyState}>
        <h2>{selectedTopic.title}</h2>
        <p>Click "Generate New Course" to create a personalized learning module.</p>
      </div>
    );
  }

  return (
    <div className={styles.content}>
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  );
};

export default ContentDisplay;
