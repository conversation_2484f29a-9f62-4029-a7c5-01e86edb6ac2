.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: #1a1a1a;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid #333;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #333;
  background: #222;
}

.header h2 {
  margin: 0;
  color: #fff;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: #888;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #333;
  color: #fff;
}

.content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.description {
  color: #ccc;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.languageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.languageOption {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 2px solid #333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #222;
}

.languageOption:hover {
  border-color: #555;
  background: #2a2a2a;
}

.languageOption:has(.radioInput:checked) {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.radioInput {
  margin-right: 12px;
  accent-color: #4CAF50;
}

.languageInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.languageName {
  color: #fff;
  font-weight: 500;
  font-size: 0.95rem;
}

.nativeName {
  color: #888;
  font-size: 0.85rem;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #333;
  background: #222;
}

.cancelButton,
.saveButton {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background: #333;
  color: #fff;
}

.cancelButton:hover {
  background: #444;
}

.saveButton {
  background: #4CAF50;
  color: white;
}

.saveButton:hover {
  background: #45a049;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal {
    width: 95%;
    margin: 20px;
  }
  
  .languageGrid {
    grid-template-columns: 1fr;
  }
  
  .header {
    padding: 16px 20px;
  }
  
  .content {
    padding: 20px;
  }
  
  .footer {
    padding: 16px 20px;
  }
}
