.editor {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #444;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #444;
}

.header h3 {
  color: #4a9eff;
  margin: 0;
  font-size: 18px;
}

.addButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #5a7fb5;
}

.curriculum {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.category {
  background-color: #333;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #555;
}

.categoryHeader {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.categoryControls {
  display: flex;
  gap: 5px;
}

.moveButton {
  background: none;
  border: 1px solid #666;
  color: #ccc;
  width: 24px;
  height: 24px;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background-color 0.2s, border-color 0.2s;
}

.moveButton:hover:not(:disabled) {
  background-color: #4a9eff;
  border-color: #4a9eff;
  color: white;
}

.moveButton:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.deleteButton {
  background: none;
  border: 1px solid #ff6b6b;
  color: #ff6b6b;
  width: 24px;
  height: 24px;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: background-color 0.2s;
}

.deleteButton:hover {
  background-color: rgba(255, 107, 107, 0.1);
}

.categoryTitle {
  color: #4a9eff;
  margin: 0;
  font-size: 16px;
  cursor: pointer;
  flex: 1;
  padding: 4px 8px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.categoryTitle:hover {
  background-color: rgba(74, 158, 255, 0.1);
}

.editInput {
  background-color: #444;
  border: 1px solid #4a9eff;
  color: #ddd;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 16px;
  font-weight: bold;
  flex: 1;
}

.editInput:focus {
  outline: none;
  border-color: #5a9fff;
}

.descriptionInput {
  width: 100%;
  background-color: #444;
  border: 1px solid #555;
  color: #ccc;
  padding: 8px;
  border-radius: 4px;
  font-size: 13px;
  font-family: inherit;
  resize: vertical;
  margin-bottom: 15px;
}

.descriptionInput:focus {
  outline: none;
  border-color: #4a9eff;
}

.topics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.topic {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #3a3a3a;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #555;
}

.topicControls {
  display: flex;
  gap: 3px;
}

.topicTitle {
  color: #ddd;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  transition: background-color 0.2s;
  min-width: 120px;
  font-weight: 500;
}

.topicTitle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.topicDescription {
  background-color: #444;
  border: 1px solid #555;
  color: #aaa;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  flex: 1;
}

.topicDescription:focus {
  outline: none;
  border-color: #4a9eff;
}

.addTopicButton {
  background: none;
  border: 1px dashed #666;
  color: #aaa;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: border-color 0.2s, color 0.2s;
  margin-top: 5px;
}

.addTopicButton:hover {
  border-color: #4a9eff;
  color: #4a9eff;
}
