import React from 'react';
import styles from './TopicTree.module.css';

// Types
interface Topic {
  id: string;
  title: string;
  [key: string]: any;
}

interface Category {
  id: string;
  title: string;
  topics: Topic[];
  [key: string]: any;
}

interface TopicTreeProps {
  curriculum: Category[] | null;
  onTopicSelect: (topic: Topic) => void;
  selectedTopic: Topic | null;
  subjectTitle?: string;
}

const TopicTree: React.FC<TopicTreeProps> = ({ curriculum, onTopicSelect, selectedTopic, subjectTitle }) => {
  const handleTopicClick = (topic: Topic): void => {
    onTopicSelect(topic);
  };

  if (!curriculum) {
    return (
      <div className={styles.topicTree}>
        <h2 className={styles.heading}>Loading...</h2>
      </div>
    );
  }

  return (
    <div className={styles.topicTree}>
      <h2 className={styles.heading}>{subjectTitle || 'Curriculum'}</h2>
      <ul className={styles.categoryList}>
        {curriculum.map((category) => (
          <li key={category.id} className={styles.category}>
            <h3 className={styles.categoryTitle}>{category.title}</h3>
            <ul className={styles.topicList}>
              {category.topics.map((topic) => (
                <li 
                  key={topic.id} 
                  className={`${styles.topic} ${selectedTopic?.id === topic.id ? styles.selected : ''}`}
                  onClick={() => handleTopicClick(topic)}
                >
                  {topic.title}
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TopicTree;
