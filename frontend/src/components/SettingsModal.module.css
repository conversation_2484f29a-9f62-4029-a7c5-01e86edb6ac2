.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: #2a2a2a;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.modalTitle {
  margin-top: 0;
  margin-bottom: 20px;
  color: #4a9eff;
}

.formGroup {
  margin-bottom: 20px;
}

.formLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #ddd;
}

.formInput {
  width: 100%;
  padding: 10px;
  background-color: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #ddd;
  font-size: 14px;
}

.formInput:focus {
  outline: none;
  border-color: #4a9eff;
}

.formHelp {
  margin-top: 8px;
  font-size: 12px;
  color: #aaa;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancelButton, .saveButton {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: bold;
}

.cancelButton {
  background-color: #444;
  color: #ddd;
}

.cancelButton:hover {
  background-color: #555;
}

.saveButton {
  background-color: #4a6fa5;
  color: white;
}

.saveButton:hover {
  background-color: #5a7fb5;
}
