import React, { useState, useEffect, useRef } from "react";
import "./CodeCanvas.css";
import Prism from "prismjs";
import "prismjs/components/prism-javascript";
import "prismjs/components/prism-css";
import "prismjs/components/prism-markup";
import "prismjs/themes/prism.css"; // You can change this to any Prism theme

const CodeCanvas = () => {
  const [html, setHtml] = useState(
    "<div>\n  <h1>Hello World!</h1>\n  <p>Edit this code to see changes</p>\n</div>",
  );
  const [css, setCss] = useState(
    "body {\n  font-family: sans-serif;\n  padding: 20px;\n}\n\nh1 {\n  color: #4a6cf7;\n}",
  );
  const [js, setJs] = useState(
    '// JavaScript code will go here\nconsole.log("Hello from JavaScript!");',
  );
  const [srcDoc, setSrcDoc] = useState("");
  const [activeTab, setActiveTab] = useState("preview");
  const iframeRef = useRef(null);
  const codeRef = useRef(null);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setSrcDoc(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>${css}</style>
        </head>
        <body>
          ${html}
          <script>${js}</script>
        </body>
        </html>
      `);
    }, 250);

    return () => clearTimeout(timeout);
  }, [html, css, js]);

  useEffect(() => {
    if (codeRef.current && activeTab !== "preview") {
      Prism.highlightElement(codeRef.current);
    }
  }, [html, css, js, activeTab]);

  const handleRunCode = () => {
    if (iframeRef.current) {
      iframeRef.current.srcdoc = srcDoc;
    }
  };

  const resetCode = () => {
    setHtml(
      "<div>\n  <h1>Hello World!</h1>\n  <p>Edit this code to see changes</p>\n</div>",
    );
    setCss(
      "body {\n  font-family: sans-serif;\n  padding: 20px;\n}\n\nh1 {\n  color: #4a6cf7;\n}",
    );
    setJs(
      '// JavaScript code will go here\nconsole.log("Hello from JavaScript!");',
    );
  };

  const getHighlightedCode = () => {
    if (activeTab === "html") return html;
    if (activeTab === "css") return css;
    if (activeTab === "js") return js;
    return "";
  };

  const getLanguageClass = () => {
    if (activeTab === "html") return "language-markup";
    if (activeTab === "css") return "language-css";
    if (activeTab === "js") return "language-javascript";
    return "";
  };

  return (
    <div className="code-canvas-container">
      <div className="code-canvas-header">
        <h2>Interactive Code Canvas</h2>
        <div className="canvas-actions">
          <button onClick={handleRunCode} className="run-btn">
            Run Code
          </button>
          <button onClick={resetCode} className="reset-btn">
            Reset
          </button>
        </div>
      </div>

      <div className="canvas-tabs">
        <button
          className={`tab-btn ${activeTab === "html" ? "active" : ""}`}
          onClick={() => setActiveTab("html")}
        >
          HTML
        </button>
        <button
          className={`tab-btn ${activeTab === "css" ? "active" : ""}`}
          onClick={() => setActiveTab("css")}
        >
          CSS
        </button>
        <button
          className={`tab-btn ${activeTab === "js" ? "active" : ""}`}
          onClick={() => setActiveTab("js")}
        >
          JavaScript
        </button>
        <button
          className={`tab-btn ${activeTab === "preview" ? "active" : ""}`}
          onClick={() => setActiveTab("preview")}
        >
          Preview
        </button>
      </div>

      <div className="canvas-content">
        {(activeTab === "html" ||
          activeTab === "css" ||
          activeTab === "js") && (
          <div className="code-editor-container">
            <div className="editor-header">
              <span>{activeTab.toUpperCase()} Editor</span>
            </div>
            <div className="code-editor-wrapper">
              <textarea
                className="code-editor-input"
                value={
                  activeTab === "html" ? html : activeTab === "css" ? css : js
                }
                onChange={(e) => {
                  if (activeTab === "html") setHtml(e.target.value);
                  if (activeTab === "css") setCss(e.target.value);
                  if (activeTab === "js") setJs(e.target.value);
                }}
                spellCheck="false"
              />
              <pre className="code-editor-highlight">
                <code ref={codeRef} className={getLanguageClass()}>
                  {getHighlightedCode()}
                </code>
              </pre>
            </div>
          </div>
        )}

        {activeTab === "preview" && (
          <div className="preview-container">
            <div className="preview-header">
              <span>Live Preview</span>
            </div>
            <iframe
              ref={iframeRef}
              srcDoc={srcDoc}
              title="preview"
              sandbox="allow-scripts"
              frameBorder="0"
              width="100%"
              height="100%"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeCanvas;
