import React, { useState } from 'react';
import styles from './SettingsModal.module.css';

function SettingsModal({ onClose, onSave, initialApiKey }) {
  const [apiKey, setApiKey] = useState(initialApiKey || '');

  const handleSave = () => {
    onSave(apiKey);
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleBackdropClick}>
      <div className={styles.modal}>
        <h2 className={styles.modalTitle}>Settings</h2>
        
        <div className={styles.formGroup}>
          <label htmlFor="apiKey" className={styles.formLabel}>
            LLM API Key
          </label>
          <input
            id="apiKey"
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            className={styles.formInput}
            placeholder="Enter your API key"
          />
          <p className={styles.formHelp}>
            Your API key is stored locally in your browser and is never sent to any server except the LLM API.
          </p>
        </div>
        
        <div className={styles.modalActions}>
          <button onClick={onClose} className={styles.cancelButton}>
            Cancel
          </button>
          <button onClick={handleSave} className={styles.saveButton}>
            Save
          </button>
        </div>
      </div>
    </div>
  );
}

export default SettingsModal;
