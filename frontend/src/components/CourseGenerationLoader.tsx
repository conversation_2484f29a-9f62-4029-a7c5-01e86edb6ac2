import React, { useState, useEffect } from 'react';
import styles from './CourseGenerationLoader.module.css';

const GENERATION_STAGES = [
  {
    id: 'analyzing',
    title: 'Analyzing Your Learning Sketch',
    description: 'Our AI is reading and understanding your learning goals, identifying key topics and skill areas.',
    icon: '🔍',
    duration: 3000,
    progress: 15
  },
  {
    id: 'personas',
    title: 'Selecting AI Specialists',
    description: 'Choosing the best AI personas for each topic area - from programming experts to domain specialists.',
    icon: '🤖',
    duration: 4000,
    progress: 30
  },
  {
    id: 'structure',
    title: 'Designing Course Architecture',
    description: 'Creating the optimal learning path with progressive modules and clear prerequisites.',
    icon: '🏗️',
    duration: 5000,
    progress: 50
  },
  {
    id: 'curricula',
    title: 'Generating Detailed Curricula',
    description: 'Each AI specialist is creating comprehensive learning materials for their expertise area.',
    icon: '📚',
    duration: 20000,
    progress: 85
  },
  {
    id: 'finalizing',
    title: 'Finalizing Your Course',
    description: 'Integrating all modules, setting learning objectives, and preparing your personalized curriculum.',
    icon: '✨',
    duration: 3000,
    progress: 100
  }
];

function CourseGenerationLoader({ isVisible, onComplete }) {
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    if (isVisible && !startTime) {
      setStartTime(Date.now());
      setCurrentStageIndex(0);
      setProgress(0);
    }
  }, [isVisible, startTime]);

  useEffect(() => {
    if (!isVisible) return;

    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, startTime]);

  useEffect(() => {
    if (!isVisible) return;

    let timeoutId;
    
    const advanceStage = () => {
      if (currentStageIndex < GENERATION_STAGES.length - 1) {
        const currentStage = GENERATION_STAGES[currentStageIndex];
        setProgress(currentStage.progress);
        
        timeoutId = setTimeout(() => {
          setCurrentStageIndex(prev => prev + 1);
        }, currentStage.duration);
      } else {
        setProgress(100);
        if (onComplete) {
          setTimeout(onComplete, 1000);
        }
      }
    };

    advanceStage();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [currentStageIndex, isVisible, onComplete]);

  if (!isVisible) return null;

  const currentStage = GENERATION_STAGES[currentStageIndex];
  const estimatedTotal = GENERATION_STAGES.reduce((sum, stage) => sum + stage.duration, 0) / 1000;

  return (
    <div className={styles.loaderContainer}>
      <div className={styles.loaderCard}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.mainIcon}>🎓</div>
          <h2>Creating Your University Course</h2>
          <p>Our AI specialists are working together to build your personalized curriculum</p>
        </div>

        {/* Progress Bar */}
        <div className={styles.progressSection}>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill} 
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className={styles.progressInfo}>
            <span className={styles.progressPercent}>{progress}%</span>
            <span className={styles.timeInfo}>
              {elapsedTime}s / ~{estimatedTotal}s
            </span>
          </div>
        </div>

        {/* Current Stage */}
        <div className={styles.currentStage}>
          <div className={styles.stageIcon}>{currentStage.icon}</div>
          <div className={styles.stageContent}>
            <h3>{currentStage.title}</h3>
            <p>{currentStage.description}</p>
          </div>
        </div>

        {/* Stage Timeline */}
        <div className={styles.timeline}>
          {GENERATION_STAGES.map((stage, index) => (
            <div 
              key={stage.id}
              className={`${styles.timelineItem} ${
                index < currentStageIndex ? styles.completed :
                index === currentStageIndex ? styles.active :
                styles.pending
              }`}
            >
              <div className={styles.timelineIcon}>{stage.icon}</div>
              <div className={styles.timelineContent}>
                <span className={styles.timelineTitle}>{stage.title}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Fun Facts */}
        <div className={styles.funFacts}>
          <div className={styles.funFact}>
            <span className={styles.factIcon}>🧠</span>
            <span>Multiple AI specialists working in parallel</span>
          </div>
          <div className={styles.funFact}>
            <span className={styles.factIcon}>📖</span>
            <span>University-level curriculum quality</span>
          </div>
          <div className={styles.funFact}>
            <span className={styles.factIcon}>🌍</span>
            <span>Content tailored to your language</span>
          </div>
        </div>

        {/* Tip */}
        <div className={styles.tip}>
          <span className={styles.tipIcon}>💡</span>
          <span>
            <strong>Tip:</strong> The more detailed your learning sketch, the more personalized your course will be!
          </span>
        </div>
      </div>
    </div>
  );
}

export default CourseGenerationLoader;
