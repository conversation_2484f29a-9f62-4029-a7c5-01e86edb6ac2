.loaderContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loaderCard {
  background: #1a1a1a;
  border-radius: 16px;
  padding: 32px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border: 2px solid #4CAF50;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 32px;
}

.mainIcon {
  font-size: 3rem;
  margin-bottom: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.header h2 {
  color: #4CAF50;
  margin: 0 0 8px 0;
  font-size: 1.8rem;
}

.header p {
  color: #ccc;
  margin: 0;
  font-size: 1rem;
  line-height: 1.4;
}

/* Progress Section */
.progressSection {
  margin-bottom: 32px;
}

.progressBar {
  width: 100%;
  height: 12px;
  background: #333;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049, #66BB6A);
  background-size: 200% 100%;
  border-radius: 6px;
  transition: width 0.8s ease;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.progressInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progressPercent {
  font-size: 1.2rem;
  font-weight: bold;
  color: #4CAF50;
}

.timeInfo {
  color: #888;
  font-size: 0.9rem;
}

/* Current Stage */
.currentStage {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #222;
  border-radius: 12px;
  border: 1px solid #333;
  margin-bottom: 32px;
}

.stageIcon {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.stageContent h3 {
  color: #4CAF50;
  margin: 0 0 8px 0;
  font-size: 1.2rem;
}

.stageContent p {
  color: #ddd;
  margin: 0;
  line-height: 1.4;
}

/* Timeline */
.timeline {
  margin-bottom: 32px;
}

.timelineItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  transition: all 0.3s ease;
}

.timelineIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.timelineContent {
  flex: 1;
}

.timelineTitle {
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

/* Timeline States */
.timelineItem.completed .timelineIcon {
  background: #4CAF50;
  color: white;
}

.timelineItem.completed .timelineTitle {
  color: #4CAF50;
}

.timelineItem.active .timelineIcon {
  background: #4CAF50;
  color: white;
  animation: pulse 2s infinite;
}

.timelineItem.active .timelineTitle {
  color: #4CAF50;
  font-weight: bold;
}

.timelineItem.pending .timelineIcon {
  background: #333;
  color: #666;
}

.timelineItem.pending .timelineTitle {
  color: #666;
}

/* Fun Facts */
.funFacts {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: 24px;
}

.funFact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #333;
}

.factIcon {
  font-size: 1.1rem;
}

.funFact span:last-child {
  color: #ddd;
  font-size: 0.9rem;
}

/* Tip */
.tip {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 16px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 8px;
  color: #ddd;
  font-size: 0.9rem;
  line-height: 1.4;
}

.tipIcon {
  font-size: 1.1rem;
  margin-top: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .loaderCard {
    padding: 24px 20px;
    margin: 20px;
  }
  
  .header h2 {
    font-size: 1.5rem;
  }
  
  .mainIcon {
    font-size: 2.5rem;
  }
  
  .currentStage {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .progressInfo {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
  
  .funFacts {
    gap: 8px;
  }
  
  .funFact {
    padding: 10px;
  }
}
