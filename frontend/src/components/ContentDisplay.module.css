.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #4a9eff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loadingText {
  margin-top: 20px;
  color: #ccc;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #ccc;
}

.emptyState h2 {
  margin-bottom: 15px;
  color: #4a9eff;
}

.content {
  color: #ddd;
  line-height: 1.6;
}

.content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  color: #4a9eff;
}

.content p {
  margin-bottom: 16px;
}

.content pre {
  background-color: #2d2d2d;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin-bottom: 16px;
}

.content code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background-color: rgba(74, 158, 255, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.content pre code {
  background-color: transparent;
  padding: 0;
}

.content ul, .content ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.content li {
  margin-bottom: 8px;
}

.content blockquote {
  border-left: 4px solid #4a9eff;
  padding-left: 16px;
  margin: 16px 0;
  color: #aaa;
}
