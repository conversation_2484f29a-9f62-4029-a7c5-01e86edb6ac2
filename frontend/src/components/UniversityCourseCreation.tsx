import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import CourseGenerationLoader from './CourseGenerationLoader';
import CourseEditor from './CourseEditor';
import styles from './UniversityCourseCreation.module.css';

function UniversityCourseCreation({ onBack, onCourseCreated }) {
  const { selectedLanguage } = useLanguage();
  const [userSketch, setUserSketch] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCourse, setGeneratedCourse] = useState(null);
  const [error, setError] = useState(null);
  const [demoMode, setDemoMode] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const handleDemoGeneration = () => {
    setIsGenerating(true);
    setError(null);
    setDemoMode(true);

    // Simulate the full generation time, then show a demo result
    setTimeout(() => {
      setGeneratedCourse({
        course: {
          title: "Demo: Full-Stack Web Development Mastery",
          description: "A comprehensive course designed to take you from beginner to advanced full-stack developer.",
          duration: "12 weeks",
          difficulty: "intermediate",
          learningOutcomes: [
            "Build complete web applications from scratch",
            "Master both frontend and backend technologies",
            "Deploy applications to production environments"
          ]
        },
        modules: [
          {
            id: "demo-module-1",
            title: "Frontend Fundamentals",
            description: "Learn HTML, CSS, and JavaScript basics",
            duration: "3 weeks",
            learningObjectives: ["Master HTML5 semantic elements", "Create responsive designs with CSS3"],
            curriculum: [
              { id: "cat-1", title: "HTML & CSS Basics", topics: [{ id: "t1", title: "HTML Structure" }] },
              { id: "cat-2", title: "JavaScript Fundamentals", topics: [{ id: "t2", title: "Variables & Functions" }] }
            ]
          }
        ]
      });
      setIsGenerating(false);
      setDemoMode(false);
    }, 35000); // Match the total animation time
  };

  const handleGenerateCourse = async () => {
    if (!userSketch.trim()) {
      setError('Please provide a learning sketch');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:3001/api/university-courses/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userSketch: userSketch.trim(),
          language: selectedLanguage.name
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setGeneratedCourse(data.course);
      console.log('Generated course:', data.course);
    } catch (err) {
      console.error('Error generating course:', err);
      setError(err.message || 'Failed to generate course. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCreateCourse = async () => {
    if (!generatedCourse) return;

    try {
      const response = await fetch('http://localhost:3001/api/university-courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: generatedCourse.course.title,
          description: generatedCourse.course.description,
          duration: generatedCourse.course.duration,
          difficulty: generatedCourse.course.difficulty,
          prerequisites: generatedCourse.course.prerequisites || [],
          learningOutcomes: generatedCourse.course.learningOutcomes,
          userSketch: userSketch,
          modules: generatedCourse.modules.map(module => ({
            title: module.title,
            description: module.description,
            duration: module.duration,
            order: module.order,
            prerequisites: module.prerequisites || [],
            learningObjectives: module.learningObjectives,
            curriculum: { curriculum: module.curriculum }
          }))
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Course created:', data);

      if (onCourseCreated) {
        onCourseCreated(data);
      }
    } catch (err) {
      console.error('Error creating course:', err);
      setError(err.message || 'Failed to create course. Please try again.');
    }
  };

  const handleEditCourse = () => {
    setIsEditing(true);
  };

  const handleSaveCustomizations = (editedCourse) => {
    setGeneratedCourse(editedCourse);
    setIsEditing(false);
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
  };

  const handleBackToGeneration = () => {
    setGeneratedCourse(null);
    setIsEditing(false);
    setError(null);
  };

  // Show course editor if in editing mode
  if (isEditing && generatedCourse) {
    return (
      <CourseEditor
        course={generatedCourse}
        onSave={handleSaveCustomizations}
        onCancel={handleCancelEditing}
        onBackToGeneration={handleBackToGeneration}
      />
    );
  }

  return (
    <div className={styles.container}>
      <CourseGenerationLoader
        isVisible={isGenerating}
        onComplete={() => {
          // This will be called when the animation completes
          // The actual API call completion is handled separately
        }}
      />

      <div className={styles.header}>
        <button className={styles.backButton} onClick={onBack}>
          ← Back
        </button>
        <h1>Create University-Style Course</h1>
        <div className={styles.languageIndicator}>
          🌐 {selectedLanguage.nativeName}
        </div>
      </div>

      {!generatedCourse ? (
        <div className={styles.sketchSection}>
          <div className={styles.instructions}>
            <h2>📝 Describe Your Learning Goals</h2>
            <p>
              Provide a detailed sketch of what you want to learn. The AI will analyze your content 
              and create a comprehensive university-style course with multiple specialized modules.
            </p>
            <div className={styles.examples}>
              <strong>Examples:</strong>
              <ul>
                <li>Technical skills you want to master</li>
                <li>Knowledge areas you want to explore</li>
                <li>Professional competencies you want to develop</li>
                <li>Academic subjects you want to study</li>
              </ul>
            </div>
          </div>

          <textarea
            className={styles.sketchInput}
            value={userSketch}
            onChange={(e) => setUserSketch(e.target.value)}
            placeholder="Describe what you want to learn in detail. For example:

### Technical Interview Preparation
- Master data structures and algorithms
- Learn system design principles
- Practice coding interview questions
- Understand database optimization
- Learn cloud architecture patterns

Be as detailed as possible about your learning goals..."
            rows={15}
          />

          {error && (
            <div className={styles.error}>
              ❌ {error}
            </div>
          )}

          <div className={styles.buttonGroup}>
            <button
              className={styles.generateButton}
              onClick={handleGenerateCourse}
              disabled={isGenerating || !userSketch.trim()}
            >
              {isGenerating ? '🔄 Generating Course...' : '🚀 Generate University Course'}
            </button>

            <button
              className={styles.demoButton}
              onClick={handleDemoGeneration}
              disabled={isGenerating}
            >
              🎬 Demo Animation
            </button>
          </div>
        </div>
      ) : (
        <div className={styles.coursePreview}>
          <div className={styles.courseHeader}>
            <h2>📚 {generatedCourse.course.title}</h2>
            <div className={styles.courseMetadata}>
              <span className={styles.duration}>⏱️ {generatedCourse.course.duration}</span>
              <span className={styles.difficulty}>📈 {generatedCourse.course.difficulty}</span>
            </div>
          </div>

          <div className={styles.courseDescription}>
            <p>{generatedCourse.course.description}</p>
          </div>

          <div className={styles.learningOutcomes}>
            <h3>🎯 Learning Outcomes</h3>
            <ul>
              {generatedCourse.course.learningOutcomes.map((outcome, index) => (
                <li key={index}>{outcome}</li>
              ))}
            </ul>
          </div>

          <div className={styles.modules}>
            <h3>📦 Course Modules ({generatedCourse.modules.length})</h3>
            {generatedCourse.modules.map((module, index) => (
              <div key={module.id} className={styles.module}>
                <div className={styles.moduleHeader}>
                  <h4>{index + 1}. {module.title}</h4>
                  <span className={styles.moduleDuration}>{module.duration}</span>
                </div>
                <p className={styles.moduleDescription}>{module.description}</p>
                
                <div className={styles.moduleObjectives}>
                  <strong>Learning Objectives:</strong>
                  <ul>
                    {module.learningObjectives.map((objective, objIndex) => (
                      <li key={objIndex}>{objective}</li>
                    ))}
                  </ul>
                </div>

                <div className={styles.moduleCurriculum}>
                  <strong>Curriculum ({module.curriculum.length} categories):</strong>
                  {module.curriculum.map((category, catIndex) => (
                    <div key={category.id} className={styles.category}>
                      <span className={styles.categoryTitle}>{category.title}</span>
                      <span className={styles.topicCount}>({category.topics.length} topics)</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className={styles.actions}>
            <button
              className={styles.editButton}
              onClick={handleEditCourse}
            >
              ✏️ Customize Course
            </button>
            <button
              className={styles.regenerateButton}
              onClick={handleBackToGeneration}
            >
              🔄 Generate New Course
            </button>
            <button
              className={styles.createButton}
              onClick={handleCreateCourse}
            >
              ✅ Create Course
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default UniversityCourseCreation;
