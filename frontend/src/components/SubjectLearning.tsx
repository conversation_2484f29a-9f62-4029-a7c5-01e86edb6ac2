import React, { useState, useEffect } from 'react';
import axios from 'axios';
import TopicTree from './TopicTree';
import ContentDisplay from './ContentDisplay';
import { useLanguage } from '../contexts/LanguageContext';
import styles from './SubjectLearning.module.css';

// Types
interface Subject {
  id: string;
  title: string;
  description?: string;
  [key: string]: any;
}

interface Topic {
  id?: string;
  title: string;
  [key: string]: any;
}

interface SubjectLearningProps {
  subject: Subject | null;
  onBack: () => void;
  apiKey: string;
}

const SubjectLearning: React.FC<SubjectLearningProps> = ({ subject, onBack, apiKey }) => {
  const { selectedLanguage } = useLanguage();
  const [curriculum, setCurriculum] = useState<any>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [courseContent, setCourseContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingCurriculum, setIsLoadingCurriculum] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (subject?.id) {
      fetchCurriculum();
    }
  }, [subject?.id]);

  const fetchCurriculum = async (): Promise<void> => {
    if (!subject?.id) return;

    try {
      setIsLoadingCurriculum(true);
      const response = await axios.get(`http://localhost:3001/api/curricula/${subject.id}`);
      setCurriculum(response.data.structure.curriculum);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching curriculum:', error);
      setError('Failed to load curriculum. Please try again.');
    } finally {
      setIsLoadingCurriculum(false);
    }
  };

  const handleTopicSelect = (topic: Topic): void => {
    setSelectedTopic(topic);
    setCourseContent(''); // Clear previous content when selecting a new topic
  };

  const handleGenerateCourse = async (): Promise<void> => {
    if (!selectedTopic) {
      alert('Please select a topic first');
      return;
    }

    if (!apiKey) {
      alert('Please set your API key in the settings');
      return;
    }

    if (!subject?.id) {
      alert('Subject not found');
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.post('http://localhost:3001/api/generate', {
        topic: selectedTopic.title,
        subjectId: subject.id,
        topicId: selectedTopic.id,
        apiKey: apiKey,
        language: selectedLanguage.name
      });

      setCourseContent(response.data.content);
    } catch (error: any) {
      console.error('Error generating course:', error);
      if (error.response) {
        alert(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        alert('Error: No response from server. Make sure the backend is running.');
      } else {
        alert(`Error: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!subject) {
    return (
      <div className={styles.errorContainer}>
        <h2>Error</h2>
        <p>Subject not found</p>
        <button onClick={onBack} className={styles.backButton}>
          Back to Dashboard
        </button>
      </div>
    );
  }

  if (isLoadingCurriculum) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p className={styles.loadingText}>Loading curriculum...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h2>Error</h2>
        <p>{error}</p>
        <div className={styles.errorActions}>
          <button onClick={fetchCurriculum} className={styles.retryButton}>
            Try Again
          </button>
          <button onClick={onBack} className={styles.backButton}>
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <button onClick={onBack} className={styles.backButton}>
          ← Back to Dashboard
        </button>
        <div className={styles.subjectInfo}>
          <h1>{subject.title}</h1>
          {subject.description && <p>{subject.description}</p>}
        </div>
      </div>

      <div className={styles.mainContent}>
        <div className={styles.sidebar}>
          <TopicTree 
            curriculum={curriculum} 
            onTopicSelect={handleTopicSelect}
            selectedTopic={selectedTopic}
            subjectTitle={subject.title}
          />
        </div>
        
        <div className={styles.contentArea}>
          {selectedTopic && (
            <div className={styles.generateSection}>
              <button 
                className={styles.generateButton}
                onClick={handleGenerateCourse}
                disabled={isLoading}
              >
                {isLoading ? 'Generating...' : 'Generate New Course'}
              </button>
            </div>
          )}
          <ContentDisplay 
            content={courseContent} 
            isLoading={isLoading}
            selectedTopic={selectedTopic}
          />
        </div>
      </div>
    </div>
  );
};

export default SubjectLearning;
