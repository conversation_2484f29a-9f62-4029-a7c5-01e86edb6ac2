import React, { useState } from 'react';
import styles from './CurriculumEditor.module.css';
import { Category, Topic } from '../types';

// Types
interface CurriculumData {
  curriculum: Category[];
  [key: string]: any;
}

interface CurriculumEditorProps {
  curriculum: CurriculumData;
  onCurriculumChange: (curriculum: CurriculumData) => void;
}

const CurriculumEditor: React.FC<CurriculumEditorProps> = ({ curriculum, onCurriculumChange }) => {
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editingTopic, setEditingTopic] = useState<string | null>(null);

  const handleAddCategory = (): void => {
    const newCategory: Category = {
      id: `category-${Date.now()}`,
      title: 'New Category',
      description: '',
      topics: []
    };

    const updatedCurriculum: CurriculumData = {
      ...curriculum,
      curriculum: [...curriculum.curriculum, newCategory]
    };

    onCurriculumChange(updatedCurriculum);
    setEditingCategory(newCategory.id);
  };

  const handleDeleteCategory = (categoryId: string): void => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.filter(cat => cat.id !== categoryId)
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleUpdateCategory = (categoryId, field, value) => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId ? { ...cat, [field]: value } : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleAddTopic = (categoryId) => {
    const newTopic = {
      id: `topic-${Date.now()}`,
      title: 'New Topic',
      description: ''
    };
    
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId 
          ? { ...cat, topics: [...cat.topics, newTopic] }
          : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
    setEditingTopic(newTopic.id);
  };

  const handleDeleteTopic = (categoryId, topicId) => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId 
          ? { ...cat, topics: cat.topics.filter(topic => topic.id !== topicId) }
          : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleUpdateTopic = (categoryId, topicId, field, value) => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId 
          ? {
              ...cat,
              topics: cat.topics.map(topic => 
                topic.id === topicId ? { ...topic, [field]: value } : topic
              )
            }
          : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleMoveCategoryUp = (categoryIndex) => {
    if (categoryIndex === 0) return;
    
    const newCurriculum = [...curriculum.curriculum];
    [newCurriculum[categoryIndex - 1], newCurriculum[categoryIndex]] = 
    [newCurriculum[categoryIndex], newCurriculum[categoryIndex - 1]];
    
    onCurriculumChange({ ...curriculum, curriculum: newCurriculum });
  };

  const handleMoveCategoryDown = (categoryIndex) => {
    if (categoryIndex === curriculum.curriculum.length - 1) return;
    
    const newCurriculum = [...curriculum.curriculum];
    [newCurriculum[categoryIndex], newCurriculum[categoryIndex + 1]] = 
    [newCurriculum[categoryIndex + 1], newCurriculum[categoryIndex]];
    
    onCurriculumChange({ ...curriculum, curriculum: newCurriculum });
  };

  const handleMoveTopicUp = (categoryId, topicIndex) => {
    if (topicIndex === 0) return;
    
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => {
        if (cat.id === categoryId) {
          const newTopics = [...cat.topics];
          [newTopics[topicIndex - 1], newTopics[topicIndex]] = 
          [newTopics[topicIndex], newTopics[topicIndex - 1]];
          return { ...cat, topics: newTopics };
        }
        return cat;
      })
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleMoveTopicDown = (categoryId, topicIndex, topicsLength) => {
    if (topicIndex === topicsLength - 1) return;
    
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => {
        if (cat.id === categoryId) {
          const newTopics = [...cat.topics];
          [newTopics[topicIndex], newTopics[topicIndex + 1]] = 
          [newTopics[topicIndex + 1], newTopics[topicIndex]];
          return { ...cat, topics: newTopics };
        }
        return cat;
      })
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  return (
    <div className={styles.editor}>
      <div className={styles.header}>
        <h3>Edit Curriculum</h3>
        <button onClick={handleAddCategory} className={styles.addButton}>
          + Add Category
        </button>
      </div>

      <div className={styles.curriculum}>
        {curriculum.curriculum.map((category, categoryIndex) => (
          <div key={category.id} className={styles.category}>
            <div className={styles.categoryHeader}>
              <div className={styles.categoryControls}>
                <button
                  onClick={() => handleMoveCategoryUp(categoryIndex)}
                  disabled={categoryIndex === 0}
                  className={styles.moveButton}
                  title="Move up"
                >
                  ↑
                </button>
                <button
                  onClick={() => handleMoveCategoryDown(categoryIndex)}
                  disabled={categoryIndex === curriculum.curriculum.length - 1}
                  className={styles.moveButton}
                  title="Move down"
                >
                  ↓
                </button>
                <button
                  onClick={() => handleDeleteCategory(category.id)}
                  className={styles.deleteButton}
                  title="Delete category"
                >
                  ×
                </button>
              </div>
              
              {editingCategory === category.id ? (
                <input
                  type="text"
                  value={category.title}
                  onChange={(e) => handleUpdateCategory(category.id, 'title', e.target.value)}
                  onBlur={() => setEditingCategory(null)}
                  onKeyPress={(e) => e.key === 'Enter' && setEditingCategory(null)}
                  className={styles.editInput}
                  autoFocus
                />
              ) : (
                <h4 
                  className={styles.categoryTitle}
                  onClick={() => setEditingCategory(category.id)}
                >
                  {category.title}
                </h4>
              )}
            </div>

            <textarea
              value={category.description}
              onChange={(e) => handleUpdateCategory(category.id, 'description', e.target.value)}
              placeholder="Category description..."
              className={styles.descriptionInput}
              rows={2}
            />

            <div className={styles.topics}>
              {category.topics.map((topic, topicIndex) => (
                <div key={topic.id} className={styles.topic}>
                  <div className={styles.topicControls}>
                    <button
                      onClick={() => handleMoveTopicUp(category.id, topicIndex)}
                      disabled={topicIndex === 0}
                      className={styles.moveButton}
                      title="Move up"
                    >
                      ↑
                    </button>
                    <button
                      onClick={() => handleMoveTopicDown(category.id, topicIndex, category.topics.length)}
                      disabled={topicIndex === category.topics.length - 1}
                      className={styles.moveButton}
                      title="Move down"
                    >
                      ↓
                    </button>
                    <button
                      onClick={() => handleDeleteTopic(category.id, topic.id)}
                      className={styles.deleteButton}
                      title="Delete topic"
                    >
                      ×
                    </button>
                  </div>
                  
                  {editingTopic === topic.id ? (
                    <input
                      type="text"
                      value={topic.title}
                      onChange={(e) => handleUpdateTopic(category.id, topic.id, 'title', e.target.value)}
                      onBlur={() => setEditingTopic(null)}
                      onKeyPress={(e) => e.key === 'Enter' && setEditingTopic(null)}
                      className={styles.editInput}
                      autoFocus
                    />
                  ) : (
                    <span 
                      className={styles.topicTitle}
                      onClick={() => setEditingTopic(topic.id)}
                    >
                      {topic.title}
                    </span>
                  )}
                  
                  <input
                    type="text"
                    value={topic.description}
                    onChange={(e) => handleUpdateTopic(category.id, topic.id, 'description', e.target.value)}
                    placeholder="Topic description..."
                    className={styles.topicDescription}
                  />
                </div>
              ))}
              
              <button
                onClick={() => handleAddTopic(category.id)}
                className={styles.addTopicButton}
              >
                + Add Topic
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CurriculumEditor;
