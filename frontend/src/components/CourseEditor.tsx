import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext.js';
import styles from './CourseEditor.module.css';

function CourseEditor({ course, onSave, onCancel, onBackToGeneration }) {
  const { selectedLanguage } = useLanguage();
  const [editedCourse, setEditedCourse] = useState({
    ...course.course,
    modules: [...course.modules]
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedModule, setExpandedModule] = useState(null);

  const handleCourseInfoChange = (field, value) => {
    setEditedCourse(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLearningOutcomeChange = (index, value) => {
    const newOutcomes = [...editedCourse.learningOutcomes];
    newOutcomes[index] = value;
    setEditedCourse(prev => ({
      ...prev,
      learningOutcomes: newOutcomes
    }));
  };

  const addLearningOutcome = () => {
    setEditedCourse(prev => ({
      ...prev,
      learningOutcomes: [...prev.learningOutcomes, 'New learning outcome']
    }));
  };

  const removeLearningOutcome = (index) => {
    const newOutcomes = editedCourse.learningOutcomes.filter((_, i) => i !== index);
    setEditedCourse(prev => ({
      ...prev,
      learningOutcomes: newOutcomes
    }));
  };

  const handleModuleChange = (moduleIndex, field, value) => {
    const newModules = [...editedCourse.modules];
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      [field]: value
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const handleObjectiveChange = (moduleIndex, objIndex, value) => {
    const newModules = [...editedCourse.modules];
    const newObjectives = [...newModules[moduleIndex].learningObjectives];
    newObjectives[objIndex] = value;
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      learningObjectives: newObjectives
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const addObjective = (moduleIndex) => {
    const newModules = [...editedCourse.modules];
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      learningObjectives: [...newModules[moduleIndex].learningObjectives, 'New learning objective']
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeObjective = (moduleIndex, objIndex) => {
    const newModules = [...editedCourse.modules];
    const newObjectives = newModules[moduleIndex].learningObjectives.filter((_, i) => i !== objIndex);
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      learningObjectives: newObjectives
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const moveModule = (fromIndex, toIndex) => {
    const newModules = [...editedCourse.modules];
    const [movedModule] = newModules.splice(fromIndex, 1);
    newModules.splice(toIndex, 0, movedModule);
    
    // Update order numbers
    newModules.forEach((module, index) => {
      module.order = index + 1;
    });
    
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeModule = (moduleIndex) => {
    if (window.confirm('Are you sure you want to remove this module? This action cannot be undone.')) {
      const newModules = editedCourse.modules.filter((_, i) => i !== moduleIndex);
      // Update order numbers
      newModules.forEach((module, index) => {
        module.order = index + 1;
      });
      setEditedCourse(prev => ({
        ...prev,
        modules: newModules
      }));
    }
  };

  const addNewModule = () => {
    const newModule = {
      id: `module-${Date.now()}`,
      title: 'New Module',
      description: 'Description for the new module',
      duration: '2 weeks',
      order: editedCourse.modules.length + 1,
      prerequisites: [],
      learningObjectives: ['New learning objective'],
      curriculum: [
        {
          id: `category-${Date.now()}`,
          title: 'New Category',
          topics: [
            {
              id: `topic-${Date.now()}`,
              title: 'New Topic'
            }
          ]
        }
      ]
    };

    setEditedCourse(prev => ({
      ...prev,
      modules: [...prev.modules, newModule]
    }));
  };

  const handleCurriculumCategoryChange = (moduleIndex, categoryIndex, field, value) => {
    const newModules = [...editedCourse.modules];
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      [field]: value
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const addCurriculumCategory = (moduleIndex) => {
    const newModules = [...editedCourse.modules];
    const newCategory = {
      id: `category-${Date.now()}`,
      title: 'New Category',
      topics: [
        {
          id: `topic-${Date.now()}`,
          title: 'New Topic'
        }
      ]
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: [...newModules[moduleIndex].curriculum, newCategory]
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeCurriculumCategory = (moduleIndex, categoryIndex) => {
    if (window.confirm('Are you sure you want to remove this curriculum category?')) {
      const newModules = [...editedCourse.modules];
      const newCurriculum = newModules[moduleIndex].curriculum.filter((_, i) => i !== categoryIndex);
      newModules[moduleIndex] = {
        ...newModules[moduleIndex],
        curriculum: newCurriculum
      };
      setEditedCourse(prev => ({
        ...prev,
        modules: newModules
      }));
    }
  };

  const handleTopicChange = (moduleIndex, categoryIndex, topicIndex, value) => {
    const newModules = [...editedCourse.modules];
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    const newTopics = [...newCurriculum[categoryIndex].topics];
    newTopics[topicIndex] = {
      ...newTopics[topicIndex],
      title: value
    };
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      topics: newTopics
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const addTopic = (moduleIndex, categoryIndex) => {
    const newModules = [...editedCourse.modules];
    const newTopic = {
      id: `topic-${Date.now()}`,
      title: 'New Topic'
    };
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      topics: [...newCurriculum[categoryIndex].topics, newTopic]
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeTopic = (moduleIndex, categoryIndex, topicIndex) => {
    const newModules = [...editedCourse.modules];
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    const newTopics = newCurriculum[categoryIndex].topics.filter((_, i) => i !== topicIndex);
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      topics: newTopics
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const handleSave = () => {
    onSave({
      course: editedCourse,
      modules: editedCourse.modules
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <button className={styles.backButton} onClick={onCancel}>
            ← Back to Preview
          </button>
          <h1>Customize Your Course</h1>
        </div>
        <div className={styles.headerRight}>
          <div className={styles.languageIndicator}>
            🌐 {selectedLanguage.nativeName}
          </div>
          <button className={styles.regenerateButton} onClick={onBackToGeneration}>
            🔄 Generate New Course
          </button>
        </div>
      </div>

      <div className={styles.tabNavigation}>
        <button 
          className={`${styles.tab} ${activeTab === 'overview' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          📚 Course Overview
        </button>
        <button 
          className={`${styles.tab} ${activeTab === 'modules' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('modules')}
        >
          📦 Modules ({editedCourse.modules.length})
        </button>
      </div>

      <div className={styles.content}>
        {activeTab === 'overview' && (
          <div className={styles.overviewTab}>
            <div className={styles.section}>
              <h3>Course Information</h3>
              <div className={styles.formGroup}>
                <label>Course Title</label>
                <input
                  type="text"
                  value={editedCourse.title}
                  onChange={(e) => handleCourseInfoChange('title', e.target.value)}
                  className={styles.input}
                />
              </div>
              
              <div className={styles.formGroup}>
                <label>Description</label>
                <textarea
                  value={editedCourse.description}
                  onChange={(e) => handleCourseInfoChange('description', e.target.value)}
                  className={styles.textarea}
                  rows={4}
                />
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Duration</label>
                  <input
                    type="text"
                    value={editedCourse.duration}
                    onChange={(e) => handleCourseInfoChange('duration', e.target.value)}
                    className={styles.input}
                  />
                </div>
                
                <div className={styles.formGroup}>
                  <label>Difficulty</label>
                  <select
                    value={editedCourse.difficulty}
                    onChange={(e) => handleCourseInfoChange('difficulty', e.target.value)}
                    className={styles.select}
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
              </div>
            </div>

            <div className={styles.section}>
              <div className={styles.sectionHeader}>
                <h3>Learning Outcomes</h3>
                <button className={styles.addButton} onClick={addLearningOutcome}>
                  + Add Outcome
                </button>
              </div>
              
              <div className={styles.outcomesList}>
                {editedCourse.learningOutcomes.map((outcome, index) => (
                  <div key={index} className={styles.outcomeItem}>
                    <textarea
                      value={outcome}
                      onChange={(e) => handleLearningOutcomeChange(index, e.target.value)}
                      className={styles.outcomeInput}
                      rows={2}
                    />
                    <button 
                      className={styles.removeButton}
                      onClick={() => removeLearningOutcome(index)}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'modules' && (
          <div className={styles.modulesTab}>
            <div className={styles.modulesHeader}>
              <div>
                <h3>Course Modules</h3>
                <p>Drag and drop to reorder modules. Click to expand and edit.</p>
              </div>
              <button className={styles.addModuleButton} onClick={addNewModule}>
                + Add New Module
              </button>
            </div>
            
            <div className={styles.modulesList}>
              {editedCourse.modules.map((module, moduleIndex) => (
                <div key={module.id} className={styles.moduleCard}>
                  <div className={styles.moduleHeader}>
                    <div className={styles.moduleInfo}>
                      <div className={styles.moduleOrder}>
                        Module {moduleIndex + 1}
                      </div>
                      <h4>{module.title}</h4>
                      <span className={styles.moduleDuration}>{module.duration}</span>
                    </div>
                    <div className={styles.moduleActions}>
                      {moduleIndex > 0 && (
                        <button 
                          className={styles.moveButton}
                          onClick={() => moveModule(moduleIndex, moduleIndex - 1)}
                        >
                          ↑
                        </button>
                      )}
                      {moduleIndex < editedCourse.modules.length - 1 && (
                        <button 
                          className={styles.moveButton}
                          onClick={() => moveModule(moduleIndex, moduleIndex + 1)}
                        >
                          ↓
                        </button>
                      )}
                      <button 
                        className={styles.expandButton}
                        onClick={() => setExpandedModule(expandedModule === moduleIndex ? null : moduleIndex)}
                      >
                        {expandedModule === moduleIndex ? '−' : '+'}
                      </button>
                      <button 
                        className={styles.deleteButton}
                        onClick={() => removeModule(moduleIndex)}
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  {expandedModule === moduleIndex && (
                    <div className={styles.moduleContent}>
                      <div className={styles.formGroup}>
                        <label>Module Title</label>
                        <input
                          type="text"
                          value={module.title}
                          onChange={(e) => handleModuleChange(moduleIndex, 'title', e.target.value)}
                          className={styles.input}
                        />
                      </div>
                      
                      <div className={styles.formGroup}>
                        <label>Description</label>
                        <textarea
                          value={module.description}
                          onChange={(e) => handleModuleChange(moduleIndex, 'description', e.target.value)}
                          className={styles.textarea}
                          rows={3}
                        />
                      </div>

                      <div className={styles.formGroup}>
                        <label>Duration</label>
                        <input
                          type="text"
                          value={module.duration}
                          onChange={(e) => handleModuleChange(moduleIndex, 'duration', e.target.value)}
                          className={styles.input}
                        />
                      </div>

                      <div className={styles.objectivesSection}>
                        <div className={styles.sectionHeader}>
                          <label>Learning Objectives</label>
                          <button 
                            className={styles.addButton}
                            onClick={() => addObjective(moduleIndex)}
                          >
                            + Add Objective
                          </button>
                        </div>
                        
                        <div className={styles.objectivesList}>
                          {module.learningObjectives.map((objective, objIndex) => (
                            <div key={objIndex} className={styles.objectiveItem}>
                              <textarea
                                value={objective}
                                onChange={(e) => handleObjectiveChange(moduleIndex, objIndex, e.target.value)}
                                className={styles.objectiveInput}
                                rows={2}
                              />
                              <button
                                className={styles.removeButton}
                                onClick={() => removeObjective(moduleIndex, objIndex)}
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className={styles.curriculumSection}>
                        <div className={styles.sectionHeader}>
                          <label>Curriculum Categories</label>
                          <button
                            className={styles.addButton}
                            onClick={() => addCurriculumCategory(moduleIndex)}
                          >
                            + Add Category
                          </button>
                        </div>

                        <div className={styles.categoriesList}>
                          {module.curriculum.map((category, categoryIndex) => (
                            <div key={category.id} className={styles.categoryCard}>
                              <div className={styles.categoryHeader}>
                                <input
                                  type="text"
                                  value={category.title}
                                  onChange={(e) => handleCurriculumCategoryChange(moduleIndex, categoryIndex, 'title', e.target.value)}
                                  className={styles.categoryTitleInput}
                                  placeholder="Category title"
                                />
                                <button
                                  className={styles.removeButton}
                                  onClick={() => removeCurriculumCategory(moduleIndex, categoryIndex)}
                                >
                                  ×
                                </button>
                              </div>

                              <div className={styles.topicsSection}>
                                <div className={styles.topicsHeader}>
                                  <span>Topics ({category.topics.length})</span>
                                  <button
                                    className={styles.addTopicButton}
                                    onClick={() => addTopic(moduleIndex, categoryIndex)}
                                  >
                                    + Add Topic
                                  </button>
                                </div>

                                <div className={styles.topicsList}>
                                  {category.topics.map((topic, topicIndex) => (
                                    <div key={topic.id} className={styles.topicItem}>
                                      <input
                                        type="text"
                                        value={topic.title}
                                        onChange={(e) => handleTopicChange(moduleIndex, categoryIndex, topicIndex, e.target.value)}
                                        className={styles.topicInput}
                                        placeholder="Topic title"
                                      />
                                      <button
                                        className={styles.removeTopicButton}
                                        onClick={() => removeTopic(moduleIndex, categoryIndex, topicIndex)}
                                      >
                                        ×
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className={styles.footer}>
        <div className={styles.footerActions}>
          <button className={styles.cancelButton} onClick={onCancel}>
            Cancel Changes
          </button>
          <button className={styles.saveButton} onClick={handleSave}>
            💾 Save Customizations
          </button>
        </div>
      </div>
    </div>
  );
}

export default CourseEditor;
