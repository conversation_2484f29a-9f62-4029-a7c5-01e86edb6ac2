import React from 'react';
import { useLanguage } from '../contexts/LanguageContext.js';
import styles from './Header.module.css';

function Header({ onSettingsClick, onLanguageClick }) {
  const { selectedLanguage } = useLanguage();

  return (
    <header className={styles.header}>
      <h1 className={styles.title}>QtMaster.io</h1>
      <div className={styles.headerButtons}>
        <button
          className={styles.languageButton}
          onClick={onLanguageClick}
          title="Change Language"
        >
          🌐 {selectedLanguage.nativeName}
        </button>
        <button
          className={styles.settingsButton}
          onClick={onSettingsClick}
        >
          Settings
        </button>
      </div>
    </header>
  );
}

export default Header;
