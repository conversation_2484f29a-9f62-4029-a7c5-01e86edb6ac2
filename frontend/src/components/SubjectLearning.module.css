.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-bottom: 1px solid #444;
  background-color: #1a1a1a;
}

.backButton {
  background: none;
  border: 1px solid #4a9eff;
  color: #4a9eff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  white-space: nowrap;
}

.backButton:hover {
  background-color: #4a9eff;
  color: white;
}

.subjectInfo {
  flex: 1;
}

.subjectInfo h1 {
  color: #4a9eff;
  margin: 0 0 5px 0;
  font-size: 24px;
}

.subjectInfo p {
  color: #ccc;
  margin: 0;
  font-size: 14px;
}

.mainContent {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background-color: #1a1a1a;
  border-right: 1px solid #444;
  overflow-y: auto;
}

.contentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #222;
}

.generateSection {
  padding: 20px;
  border-bottom: 1px solid #444;
  background-color: #1a1a1a;
}

.generateButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.generateButton:hover:not(:disabled) {
  background-color: #5a7fb5;
}

.generateButton:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #4a9eff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loadingText {
  margin-top: 20px;
  color: #ccc;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  color: #ccc;
  padding: 20px;
}

.errorContainer h2 {
  color: #ff6b6b;
  margin-bottom: 10px;
}

.errorActions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.retryButton {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.retryButton:hover {
  background-color: #5a7fb5;
}
